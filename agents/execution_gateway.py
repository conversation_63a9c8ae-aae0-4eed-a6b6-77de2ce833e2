#!/usr/bin/env python3
"""
Execution Gateway

Unified gateway for all execution functionality. This module acts as the single
entry point for other agents to interact with the modular execution system.

Features:
- Unified API for all execution operations
- ML-based execution optimization
- Advanced order management
- Enhanced error handling and robustness
- Performance monitoring and analytics
- Comprehensive execution lifecycle management
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime
from dataclasses import dataclass
import threading

# Import base agent
from agents.base_agent import BaseAgent
from utils.event_bus import EventBus, Event, EventTypes

# Import execution core modules
try:
    from agents.execution.core.ml_execution_optimizer import MLExecutionOptimizer, ExecutionRecommendation, SlippagePrediction
    from agents.execution.core.advanced_order_manager import AdvancedOrderManager, AdvancedOrder, OrderType, OrderStatus
    from agents.execution.core.error_handler import EnhancedErrorHandler, ErrorRecord, CircuitBreakerState
    from agents.execution.core.performance_monitor import PerformanceMonitor, SlippageAnalysis, ExecutionQualityScore
    EXECUTION_MODULES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"[WARN] Execution modules not available: {e}")
    EXECUTION_MODULES_AVAILABLE = False

# Import execution models
from utils.execution_models import (
    ExecutionRequest, ExecutionResult, ExecutionStatus,
    OrderRequest, OrderResult, ExecutionMetrics
)

logger = logging.getLogger(__name__)


@dataclass
class ExecutionSystemStatus:
    """Overall execution system status"""
    system_operational: bool
    modules_loaded: Dict[str, bool]
    execution_allowed: bool
    active_orders_count: int
    completed_orders_count: int
    avg_execution_time_ms: float
    avg_slippage_bps: float
    success_rate_percent: float
    error_rate_percent: float
    last_update: datetime
    warnings: List[str]
    errors: List[str]


class ExecutionGateway(BaseAgent):
    """
    Execution Gateway - Unified interface for all execution operations
    
    This gateway provides a single entry point for:
    - Order creation and management
    - Execution optimization and routing
    - Performance monitoring and analytics
    - Error handling and recovery
    - ML-based execution insights
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        """Initialize Execution Gateway"""
        super().__init__("ExecutionGateway", event_bus, config, session_id)
        
        # Configuration
        self.config_dict = self._convert_config_to_dict(config)
        
        # Initialize core modules
        self.modules_initialized = False
        self.initialization_lock = threading.Lock()
        
        # Core modules (will be initialized in setup)
        self.ml_optimizer: Optional[MLExecutionOptimizer] = None
        self.order_manager: Optional[AdvancedOrderManager] = None
        self.error_handler: Optional[EnhancedErrorHandler] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        
        # System state
        self.system_operational = False
        self.execution_allowed = True
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        
        # Performance metrics
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_performance_update = datetime.now()
        
        logger.info("[INIT] Execution Gateway initialized")
    
    async def setup(self):
        """Setup the execution gateway and all modules"""
        try:
            await super().setup()
            
            if not EXECUTION_MODULES_AVAILABLE:
                logger.error("[ERROR] Execution modules not available")
                return False
            
            with self.initialization_lock:
                if self.modules_initialized:
                    return True
                
                logger.info("[SETUP] Initializing execution modules...")
                
                # Initialize core modules
                self.ml_optimizer = MLExecutionOptimizer(self.config_dict)
                self.order_manager = AdvancedOrderManager(self.config_dict)
                self.error_handler = EnhancedErrorHandler(self.config_dict)
                self.performance_monitor = PerformanceMonitor(self.config_dict)
                
                # Setup modules
                await self.ml_optimizer.setup()
                
                # Register health checks
                self._setup_health_checks()
                
                # Register order callbacks
                self._setup_order_callbacks()
                
                # Subscribe to relevant events
                await self._setup_event_subscriptions()
                
                self.modules_initialized = True
                self.system_operational = True
                
                logger.info("[SUCCESS] Execution gateway setup completed")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Execution gateway setup failed: {e}")
            self.system_operational = False
            return False
    
    async def execute_order(
        self, 
        execution_request: ExecutionRequest,
        market_data: Optional[Dict[str, Any]] = None
    ) -> ExecutionResult:
        """
        Execute order with comprehensive optimization and monitoring
        
        Args:
            execution_request: Execution request details
            market_data: Real-time market data
            
        Returns:
            ExecutionResult with execution outcome
        """
        try:
            if not self.system_operational:
                return self._create_error_result(
                    execution_request, "Execution system not operational", ExecutionStatus.FAILED
                )
            
            if not self.execution_allowed:
                return self._create_error_result(
                    execution_request, "Execution not allowed", ExecutionStatus.REJECTED
                )
            
            self.execution_count += 1
            execution_start = datetime.now()
            
            # Start performance tracking
            await self.performance_monitor.record_execution_start(
                order_id=execution_request.order_id,
                symbol=execution_request.symbol,
                venue=execution_request.venue or "NSE",
                strategy=execution_request.strategy_name or "default",
                order_details={
                    'quantity': execution_request.quantity,
                    'price': execution_request.price,
                    'order_type': execution_request.order_type
                }
            )
            
            # Get ML-based execution recommendation
            execution_recommendation = await self.ml_optimizer.recommend_execution_algorithm(
                symbol=execution_request.symbol,
                quantity=execution_request.quantity,
                side=execution_request.side,
                urgency=execution_request.urgency or 0.5,
                market_data=market_data or {}
            )
            
            # Predict slippage
            slippage_prediction = await self.ml_optimizer.predict_slippage(
                symbol=execution_request.symbol,
                quantity=execution_request.quantity,
                side=execution_request.side,
                market_data=market_data or {}
            )
            
            # Adaptive order sizing
            optimized_quantity, sizing_reason = await self.ml_optimizer.adaptive_order_sizing(
                base_quantity=execution_request.quantity,
                symbol=execution_request.symbol,
                market_data=market_data or {},
                risk_tolerance=execution_request.risk_tolerance or 0.5
            )
            
            # Create advanced order
            advanced_order = await self.order_manager.create_order(
                symbol=execution_request.symbol,
                side=execution_request.side,
                quantity=optimized_quantity,
                order_type=OrderType(execution_recommendation.algorithm.value),
                price=execution_request.price,
                order_id=execution_request.order_id,
                strategy_name=execution_request.strategy_name,
                tags={
                    'original_quantity': execution_request.quantity,
                    'sizing_reason': sizing_reason,
                    'predicted_slippage': slippage_prediction.predicted_slippage_bps,
                    'execution_algorithm': execution_recommendation.algorithm.value
                }
            )
            
            # Execute with circuit breaker protection
            success, execution_result = await self.error_handler.execute_with_circuit_breaker(
                operation_name=f"execute_order_{execution_request.symbol}",
                operation=self._execute_order_internal,
                advanced_order,
                execution_recommendation,
                market_data
            )
            
            if success:
                self.success_count += 1
                
                # Record execution completion
                execution_time = (datetime.now() - execution_start).total_seconds() * 1000
                await self.performance_monitor.record_execution_complete(
                    order_id=execution_request.order_id,
                    execution_details={
                        'expected_price': execution_request.price,
                        'actual_price': execution_result.get('fill_price', execution_request.price),
                        'requested_quantity': execution_request.quantity,
                        'filled_quantity': execution_result.get('filled_quantity', 0),
                        'commission': execution_result.get('commission', 0),
                        'side': execution_request.side,
                        'trade_value': execution_result.get('trade_value', 0)
                    }
                )
                
                # Publish success event
                await self.event_bus.publish(
                    EventTypes.ORDER_EXECUTED,
                    {
                        "execution_request": execution_request,
                        "execution_result": execution_result,
                        "execution_time_ms": execution_time,
                        "slippage_prediction": slippage_prediction,
                        "execution_recommendation": execution_recommendation
                    },
                    source=self.name
                )
                
                return ExecutionResult(
                    order_id=execution_request.order_id,
                    status=ExecutionStatus.COMPLETED,
                    filled_quantity=execution_result.get('filled_quantity', optimized_quantity),
                    average_price=execution_result.get('fill_price', execution_request.price),
                    commission=execution_result.get('commission', 0),
                    execution_time_ms=execution_time,
                    slippage_bps=execution_result.get('slippage_bps', 0),
                    venue=execution_result.get('venue', execution_request.venue),
                    execution_details={
                        'algorithm_used': execution_recommendation.algorithm.value,
                        'predicted_slippage': slippage_prediction.predicted_slippage_bps,
                        'actual_slippage': execution_result.get('slippage_bps', 0),
                        'sizing_adjustment': sizing_reason,
                        'quality_score': execution_result.get('quality_score', 0)
                    },
                    timestamp=datetime.now()
                )
            else:
                self.error_count += 1
                return self._create_error_result(
                    execution_request, f"Execution failed: {execution_result}", ExecutionStatus.FAILED
                )
                
        except Exception as e:
            self.error_count += 1
            logger.error(f"[ERROR] Order execution error: {e}")
            
            # Handle error through error handler
            context = {
                'order_id': execution_request.order_id,
                'symbol': execution_request.symbol,
                'quantity': execution_request.quantity
            }
            should_retry, recovery_result = await self.error_handler.handle_error(
                e, context, "execute_order"
            )
            
            return self._create_error_result(
                execution_request, f"Execution system error: {str(e)}", ExecutionStatus.FAILED
            )
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive order status"""
        try:
            if not self.order_manager:
                return None
            
            return self.order_manager.get_order_status(order_id)
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting order status: {e}")
            return None
    
    async def cancel_order(self, order_id: str, reason: str = "User requested") -> bool:
        """Cancel active order"""
        try:
            if not self.order_manager:
                return False
            
            success = await self.order_manager.cancel_order(order_id, reason)
            
            if success:
                await self.event_bus.publish(
                    EventTypes.ORDER_CANCELLED,
                    {"order_id": order_id, "reason": reason},
                    source=self.name
                )
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error cancelling order: {e}")
            return False
    
    async def get_execution_analytics(
        self, 
        symbol: Optional[str] = None,
        time_window: str = "1h"
    ) -> Dict[str, Any]:
        """Get comprehensive execution analytics"""
        try:
            if not self.performance_monitor:
                return {}
            
            from agents.execution.core.performance_monitor import TimeWindow
            
            # Convert time window string to enum
            time_window_enum = TimeWindow.HOUR  # Default
            if time_window == "1m":
                time_window_enum = TimeWindow.MINUTE
            elif time_window == "5m":
                time_window_enum = TimeWindow.FIVE_MINUTES
            elif time_window == "15m":
                time_window_enum = TimeWindow.FIFTEEN_MINUTES
            elif time_window == "1h":
                time_window_enum = TimeWindow.HOUR
            elif time_window == "1d":
                time_window_enum = TimeWindow.DAY
            elif time_window == "1w":
                time_window_enum = TimeWindow.WEEK
            
            # Get performance summary
            performance_summary = self.performance_monitor.get_performance_summary(time_window_enum)
            
            # Get slippage analysis
            slippage_analysis = await self.performance_monitor.analyze_slippage(
                symbol=symbol, time_window=time_window_enum
            )
            
            # Get ML optimizer summary
            ml_summary = await self.ml_optimizer.get_optimization_summary()
            
            # Get order manager summary
            order_summary = self.order_manager.get_manager_summary()
            
            # Get error handler summary
            error_summary = self.error_handler.get_error_summary()
            
            return {
                'performance_summary': performance_summary,
                'slippage_analysis': {
                    'symbol': slippage_analysis.symbol,
                    'avg_slippage_bps': slippage_analysis.avg_slippage_bps,
                    'median_slippage_bps': slippage_analysis.median_slippage_bps,
                    'sample_count': slippage_analysis.sample_count,
                    'slippage_by_venue': slippage_analysis.slippage_by_venue
                },
                'ml_optimization': ml_summary,
                'order_management': order_summary,
                'error_handling': error_summary,
                'system_metrics': {
                    'total_executions': self.execution_count,
                    'success_rate': (self.success_count / self.execution_count * 100) if self.execution_count > 0 else 0,
                    'error_rate': (self.error_count / self.execution_count * 100) if self.execution_count > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting execution analytics: {e}")
            return {'error': str(e)}

    def get_system_status(self) -> ExecutionSystemStatus:
        """Get comprehensive system status"""
        try:
            # Check module status
            modules_status = {
                'ml_optimizer': self.ml_optimizer is not None,
                'order_manager': self.order_manager is not None,
                'error_handler': self.error_handler is not None,
                'performance_monitor': self.performance_monitor is not None
            }

            # Get order counts
            active_orders = 0
            completed_orders = 0
            if self.order_manager:
                order_summary = self.order_manager.get_manager_summary()
                active_orders = order_summary.get('active_orders_count', 0)
                completed_orders = order_summary.get('completed_orders_count', 0)

            # Calculate performance metrics
            success_rate = (self.success_count / self.execution_count * 100) if self.execution_count > 0 else 0
            error_rate = (self.error_count / self.execution_count * 100) if self.execution_count > 0 else 0

            # Get recent performance data
            avg_execution_time = 100.0  # Default
            avg_slippage = 5.0  # Default

            if self.performance_monitor:
                from agents.execution.core.performance_monitor import TimeWindow
                perf_summary = self.performance_monitor.get_performance_summary(TimeWindow.HOUR)

                latency_metrics = perf_summary.get('metrics', {}).get('latency', {})
                avg_execution_time = latency_metrics.get('average', 100.0)

                slippage_metrics = perf_summary.get('metrics', {}).get('slippage', {})
                avg_slippage = slippage_metrics.get('average', 5.0)

            # Collect warnings and errors
            warnings = []
            errors = []

            if not self.system_operational:
                errors.append("System not operational")

            if not self.execution_allowed:
                warnings.append("Execution currently disabled")

            if error_rate > 10:
                warnings.append(f"High error rate: {error_rate:.1f}%")

            if avg_slippage > 10:
                warnings.append(f"High average slippage: {avg_slippage:.1f} bps")

            return ExecutionSystemStatus(
                system_operational=self.system_operational,
                modules_loaded=modules_status,
                execution_allowed=self.execution_allowed,
                active_orders_count=active_orders,
                completed_orders_count=completed_orders,
                avg_execution_time_ms=avg_execution_time,
                avg_slippage_bps=avg_slippage,
                success_rate_percent=success_rate,
                error_rate_percent=error_rate,
                last_update=datetime.now(),
                warnings=warnings,
                errors=errors
            )

        except Exception as e:
            logger.error(f"[ERROR] Error getting system status: {e}")
            return ExecutionSystemStatus(
                False, {}, False, 0, 0, 0, 0, 0, 100,
                datetime.now(), [], [f"Status error: {str(e)}"]
            )

    # Internal helper methods
    async def _execute_order_internal(
        self,
        advanced_order: AdvancedOrder,
        execution_recommendation: ExecutionRecommendation,
        market_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Internal order execution logic"""
        try:
            # Submit order through order manager
            success = await self.order_manager.submit_order(
                advanced_order.order_id, market_data
            )

            if not success:
                raise Exception("Order submission failed")

            # Simulate execution (in real implementation, this would integrate with broker API)
            await asyncio.sleep(0.1)  # Simulate execution delay

            # Simulate execution result
            fill_price = advanced_order.price or 100.0
            filled_quantity = advanced_order.quantity
            commission = filled_quantity * fill_price * 0.0003  # 0.03% commission

            # Calculate actual slippage (simplified)
            expected_price = advanced_order.price or fill_price
            actual_slippage = abs(fill_price - expected_price) / expected_price * 10000

            # Simulate order execution
            from agents.execution.core.advanced_order_manager import OrderExecution
            execution = OrderExecution(
                execution_id=f"EXEC_{advanced_order.order_id}",
                timestamp=datetime.now(),
                quantity=filled_quantity,
                price=fill_price,
                commission=commission,
                exchange_order_id=f"EXC_{advanced_order.order_id}",
                execution_venue=advanced_order.preferred_venue or "NSE",
                liquidity_flag="taker"
            )

            # Handle execution in order manager
            await self.order_manager.handle_execution(
                advanced_order.order_id,
                {
                    'execution_id': execution.execution_id,
                    'timestamp': execution.timestamp,
                    'quantity': execution.quantity,
                    'price': execution.price,
                    'commission': execution.commission,
                    'exchange_order_id': execution.exchange_order_id,
                    'venue': execution.execution_venue,
                    'liquidity_flag': execution.liquidity_flag
                }
            )

            return {
                'filled_quantity': filled_quantity,
                'fill_price': fill_price,
                'commission': commission,
                'slippage_bps': actual_slippage,
                'venue': execution.execution_venue,
                'trade_value': filled_quantity * fill_price,
                'quality_score': 85.0  # Simplified quality score
            }

        except Exception as e:
            logger.error(f"[ERROR] Internal order execution failed: {e}")
            raise

    def _setup_health_checks(self):
        """Setup health checks for all modules"""
        try:
            if self.error_handler:
                # Register health checks
                self.error_handler.register_health_check(
                    'ml_optimizer',
                    lambda: self.ml_optimizer is not None
                )

                self.error_handler.register_health_check(
                    'order_manager',
                    lambda: self.order_manager is not None
                )

                self.error_handler.register_health_check(
                    'performance_monitor',
                    lambda: self.performance_monitor is not None
                )

                logger.info("[HEALTH] Health checks registered")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up health checks: {e}")

    def _setup_order_callbacks(self):
        """Setup order event callbacks"""
        try:
            if self.order_manager:
                # Register callbacks for order events
                self.order_manager.register_callback(
                    'order_filled',
                    self._handle_order_filled
                )

                self.order_manager.register_callback(
                    'order_cancelled',
                    self._handle_order_cancelled
                )

                logger.info("[CALLBACKS] Order callbacks registered")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up order callbacks: {e}")

    async def _setup_event_subscriptions(self):
        """Setup event subscriptions"""
        try:
            # Subscribe to execution requests
            await self.event_bus.subscribe(
                EventTypes.EXECUTION_REQUEST,
                self._handle_execution_request
            )

            # Subscribe to market data updates
            await self.event_bus.subscribe(
                EventTypes.MARKET_DATA_UPDATE,
                self._handle_market_data_update
            )

            logger.info("[EVENTS] Event subscriptions setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up event subscriptions: {e}")

    # Event handlers
    async def _handle_execution_request(self, event: Event):
        """Handle execution request event"""
        try:
            request_data = event.data

            # Convert to ExecutionRequest
            execution_request = ExecutionRequest(
                order_id=request_data.get('order_id'),
                symbol=request_data.get('symbol'),
                side=request_data.get('side'),
                quantity=request_data.get('quantity'),
                price=request_data.get('price'),
                order_type=request_data.get('order_type', 'LIMIT'),
                venue=request_data.get('venue'),
                strategy_name=request_data.get('strategy_name'),
                urgency=request_data.get('urgency', 0.5),
                risk_tolerance=request_data.get('risk_tolerance', 0.5)
            )

            # Execute order
            result = await self.execute_order(execution_request, request_data.get('market_data'))

            # Publish result
            await self.event_bus.publish(
                EventTypes.EXECUTION_RESULT,
                {"execution_result": result},
                source=self.name
            )

        except Exception as e:
            logger.error(f"[ERROR] Error handling execution request: {e}")

    async def _handle_market_data_update(self, event: Event):
        """Handle market data update"""
        try:
            market_data = event.data

            # Update ML optimizer with new market data
            if self.ml_optimizer:
                # This would update the ML models with new market data
                pass

        except Exception as e:
            logger.error(f"[ERROR] Error handling market data update: {e}")

    async def _handle_order_filled(self, order: AdvancedOrder):
        """Handle order filled callback"""
        try:
            await self.event_bus.publish(
                EventTypes.ORDER_FILLED,
                {
                    "order_id": order.order_id,
                    "symbol": order.symbol,
                    "filled_quantity": order.filled_quantity,
                    "average_price": order.average_fill_price
                },
                source=self.name
            )

        except Exception as e:
            logger.error(f"[ERROR] Error handling order filled: {e}")

    async def _handle_order_cancelled(self, order: AdvancedOrder):
        """Handle order cancelled callback"""
        try:
            await self.event_bus.publish(
                EventTypes.ORDER_CANCELLED,
                {
                    "order_id": order.order_id,
                    "symbol": order.symbol,
                    "reason": order.tags.get('cancellation_reason', 'Unknown')
                },
                source=self.name
            )

        except Exception as e:
            logger.error(f"[ERROR] Error handling order cancelled: {e}")

    # Utility methods
    def _convert_config_to_dict(self, config: Any) -> Dict[str, Any]:
        """Convert config object to dictionary"""
        try:
            if isinstance(config, dict):
                return config
            elif hasattr(config, '__dict__'):
                return vars(config)
            else:
                # Try to extract common config attributes
                config_dict = {}
                for attr in ['execution', 'ml_execution', 'order_management',
                           'error_handling', 'performance_monitoring']:
                    if hasattr(config, attr):
                        config_dict[attr] = getattr(config, attr)
                return config_dict

        except Exception as e:
            logger.error(f"[ERROR] Error converting config to dict: {e}")
            return {}

    def _create_error_result(
        self,
        execution_request: ExecutionRequest,
        error_message: str,
        status: ExecutionStatus
    ) -> ExecutionResult:
        """Create error execution result"""
        return ExecutionResult(
            order_id=execution_request.order_id,
            status=status,
            filled_quantity=0,
            average_price=0.0,
            commission=0.0,
            execution_time_ms=0.0,
            slippage_bps=0.0,
            venue=execution_request.venue,
            execution_details={'error': error_message},
            timestamp=datetime.now()
        )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get gateway performance metrics"""
        return {
            'execution_count': self.execution_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': (self.success_count / self.execution_count * 100) if self.execution_count > 0 else 0,
            'system_operational': self.system_operational,
            'modules_initialized': self.modules_initialized,
            'active_executions': len(self.active_executions),
            'last_update': self.last_performance_update.isoformat()
        }
